
> @workflow-mapper/shared@0.0.1 test:coverage /Users/<USER>/tmp/kloudi-swe-agent/packages/shared
> vitest run --coverage

[?25l[31mWebSocket server error: Port is already in use[39m

[1m[46m RUN [49m[22m [36mv3.1.4 [39m[90m/Users/<USER>/tmp/kloudi-swe-agent/packages/shared[39m
      [2mCoverage enabled with [22m[33mv8[39m

[?2026h
[1m[33m ❯ [39m[22msrc/Result.test.ts[2m [queued][22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (1)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (0)[39m
[2m   Start at [22m18:57:09
[2m   Duration [22m200ms
[?2026l[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/Result.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 2[2mms[22m[39m
   [32m✓[39m Result Type[2m > [22mshould create successful result[32m 1[2mms[22m[39m
   [32m✓[39m Result Type[2m > [22mshould create error result[32m 0[2mms[22m[39m
   [32m✓[39m Result Type[2m > [22mshould work with type guards[32m 0[2mms[22m[39m

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m   Start at [22m 18:57:09
[2m   Duration [22m 353ms[2m (transform 45ms, setup 0ms, collect 38ms, tests 2ms, environment 0ms, prepare 76ms)[22m

[34m % [39m[2mCoverage report from [22m[33mv8[39m
-----------|---------|----------|---------|---------|-------------------
File       | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
-----------|---------|----------|---------|---------|-------------------
[32;1mAll files [0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
[32;1m Result.ts[0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
-----------|---------|----------|---------|---------|-------------------
[?25h
