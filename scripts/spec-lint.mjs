#!/usr/bin/env node

/**
 * Spec Lint Script
 * 
 * Validates milestone specification files for completeness and consistency.
 */

import { readFileSync } from 'fs';
import matter from 'gray-matter';

const specFile = process.argv[2];

if (!specFile) {
  console.error('Usage: node scripts/spec-lint.mjs <spec-file>');
  process.exit(1);
}

console.log(`🔍 Linting specification: ${specFile}`);

try {
  const content = readFileSync(specFile, 'utf8');
  const { data: frontmatter, content: body } = matter(content);
  
  let errors = 0;
  
  // Check required frontmatter fields
  const requiredFields = ['title', 'description', 'created', 'updated', 'version', 'status', 'tags', 'authors'];
  
  console.log('\n📋 Checking frontmatter...');
  for (const field of requiredFields) {
    if (!frontmatter[field]) {
      console.log(`❌ Missing frontmatter field: ${field}`);
      errors++;
    } else {
      console.log(`✅ Found: ${field}`);
    }
  }
  
  // Check required sections
  const requiredSections = [
    '## 🎯 Goal',
    '## 📦 Deliverables',
    '## 🧳 Toolchain Versions',
    '## 🗂 Directory Layout',
    '## 🧠 Key Decisions',
    '## ✅ Success Criteria',
    '## 🔨 Task Breakdown',
    '## 🧪 Acceptance Tests',
    '## 🔄 Document History'
  ];
  
  console.log('\n📑 Checking required sections...');
  for (const section of requiredSections) {
    if (!body.includes(section)) {
      console.log(`❌ Missing section: ${section}`);
      errors++;
    } else {
      console.log(`✅ Found: ${section}`);
    }
  }
  
  // Check status progression
  console.log('\n📊 Checking status...');
  const validStatuses = ['Draft', 'Approved', 'In Progress', 'In Review', 'Completed'];
  if (!validStatuses.includes(frontmatter.status)) {
    console.log(`❌ Invalid status: ${frontmatter.status}. Must be one of: ${validStatuses.join(', ')}`);
    errors++;
  } else {
    console.log(`✅ Valid status: ${frontmatter.status}`);
  }
  
  // Check version format
  console.log('\n🔢 Checking version format...');
  const versionRegex = /^\d+\.\d+\.\d+$/;
  if (!versionRegex.test(frontmatter.version)) {
    console.log(`❌ Invalid version format: ${frontmatter.version}. Must be semver (x.y.z)`);
    errors++;
  } else {
    console.log(`✅ Valid version: ${frontmatter.version}`);
  }
  
  // Summary
  console.log('\n📊 Lint Summary:');
  if (errors === 0) {
    console.log('✅ Spec passed lint');
    process.exit(0);
  } else {
    console.log(`❌ ${errors} error(s) found`);
    console.log('🔧 Please fix the issues above');
    process.exit(1);
  }
  
} catch (error) {
  console.error(`❌ Error reading spec file: ${error.message}`);
  process.exit(1);
}
