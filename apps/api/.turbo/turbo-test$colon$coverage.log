
> @workflow-mapper/api@0.0.1 test:coverage /Users/<USER>/tmp/kloudi-swe-agent/apps/api
> jest --coverage

[1m[2mDetermining test suites to run...[22m[22m[999D[K

[K
[1A[999D[K

[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A[0m[7m[1m[32m PASS [39m[22m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A  API Health Endpoint

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A    [32m✓[39m [2mshould return 200 OK with status (12 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A[999D[K[1m[2mRunning coverage on untested files...[22m[22m[999D[K----------|---------|----------|---------|---------|-------------------
File      | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
----------|---------|----------|---------|---------|-------------------
[32;1mAll files[0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
[32;1m app.ts  [0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
----------|---------|----------|---------|---------|-------------------
[1mTest Suites: [22m[1m[32m1 passed[39m[22m, 1 total
[1mTests:       [22m[1m[32m1 passed[39m[22m, 1 total
[1mSnapshots:   [22m0 total
[1mTime:[22m        1.584 s, estimated 2 s
[2mRan all test suites[22m[2m.[22m
