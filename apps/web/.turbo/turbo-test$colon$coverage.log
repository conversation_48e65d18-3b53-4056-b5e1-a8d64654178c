
> @workflow-mapper/web@0.0.1 test:coverage /Users/<USER>/tmp/kloudi-swe-agent/apps/web
> vitest run --coverage

[?25l
[1m[46m RUN [49m[22m [36mv3.1.4 [39m[90m/Users/<USER>/tmp/kloudi-swe-agent/apps/web[39m
      [2mCoverage enabled with [22m[33mv8[39m

[?2026h
[1m[33m ❯ [39m[22msrc/App.test.tsx[2m [queued][22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (1)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (0)[39m
[2m   Start at [22m18:57:08
[2m   Duration [22m816ms
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/App.test.tsx[2m 0/1[22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (1)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (1)[39m
[2m   Start at [22m18:57:08
[2m   Duration [22m1.12s
[?2026l[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/App.test.tsx [2m([22m[2m1 test[22m[2m)[22m[32m 16[2mms[22m[39m
   [32m✓[39m App Component[2m > [22mrenders welcome message[32m 15[2mms[22m[39m

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m   Start at [22m 18:57:08
[2m   Duration [22m 1.22s[2m (transform 30ms, setup 91ms, collect 131ms, tests 16ms, environment 597ms, prepare 81ms)[22m

[34m % [39m[2mCoverage report from [22m[33mv8[39m
----------|---------|----------|---------|---------|-------------------
File      | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
----------|---------|----------|---------|---------|-------------------
[32;1mAll files[0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
[32;1m App.tsx [0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
----------|---------|----------|---------|---------|-------------------
[?25h
