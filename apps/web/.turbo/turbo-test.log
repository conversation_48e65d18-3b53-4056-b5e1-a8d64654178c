
> @workflow-mapper/web@0.0.1 test /Users/<USER>/tmp/kloudi-swe-agent/apps/web
> vitest run

[?25l
[1m[46m RUN [49m[22m [36mv3.1.4 [39m[90m/Users/<USER>/tmp/kloudi-swe-agent/apps/web[39m

[?2026h
[1m[33m ❯ [39m[22msrc/App.test.tsx[2m [queued][22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (1)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (0)[39m
[2m   Start at [22m19:07:02
[2m   Duration [22m809ms
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/App.test.tsx[2m [queued][22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (1)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (0)[39m
[2m   Start at [22m19:07:02
[2m   Duration [22m909ms
[?2026l[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/App.test.tsx [2m([22m[2m1 test[22m[2m)[22m[32m 14[2mms[22m[39m
   [32m✓[39m App Component[2m > [22mrenders welcome message[32m 14[2mms[22m[39m

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m   Start at [22m 19:07:02
[2m   Duration [22m 1.05s[2m (transform 44ms, setup 80ms, collect 122ms, tests 14ms, environment 514ms, prepare 86ms)[22m

[?25h
