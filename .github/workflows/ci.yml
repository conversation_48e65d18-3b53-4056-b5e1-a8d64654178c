name: CI
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - uses: actions/setup-node@v4
        with: { node-version: 20.11.0, cache: pnpm }
      - run: corepack enable
      - run: pnpm install --frozen-lockfile
      - run: pnpm lint
      - run: pnpm type-check
      - run: pnpm test --recursive
      - run: pnpm test:coverage
      - run: pnpm build
      - run: node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.mdx
      - run: pnpm run agent:dry-run
      - run: bash scripts/m0-acceptance.sh
