/*!
 * Chai - test utility
 * Copyright(c) 2012-2014 <PERSON> <<EMAIL>>
 * MIT Licensed
 */

import {flag} from './flag.js';

/**
 * ### .test(object, expression)
 *
 * Test an object for expression.
 *
 * @param {object} obj (constructed Assertion)
 * @param {unknown} args
 * @returns {unknown}
 * @namespace Utils
 * @name test
 */
export function test(obj, args) {
  var negate = flag(obj, 'negate'),
    expr = args[0];
  return negate ? !expr : expr;
}
