'use strict';

var hmr = require('./chunk-hmr.cjs');
require('node:events');
require('debug');
require('./chunk-browser.cjs');
require('./utils.cjs');
require('node:fs');
require('node:module');
require('node:url');
require('pathe');



exports.createHmrEmitter = hmr.createHmrEmitter;
exports.createHotContext = hmr.createHotContext;
exports.getCache = hmr.getCache;
exports.handleMessage = hmr.handleMessage;
exports.reload = hmr.reload;
exports.sendMessageBuffer = hmr.sendMessageBuffer;
exports.viteNodeHmrPlugin = hmr.viteNodeHmrPlugin;
