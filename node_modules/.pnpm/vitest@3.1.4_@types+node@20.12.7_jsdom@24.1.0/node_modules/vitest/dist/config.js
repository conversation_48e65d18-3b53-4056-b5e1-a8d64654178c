export { d as defaultBrowserPort, e as extraInlineDeps } from './chunks/constants.BZZyIeIE.js';
export { c as configDefaults, a as coverageConfigDefaults, d as defaultExclude, b as defaultInclude } from './chunks/defaults.DSxsTG0h.js';
export { mergeConfig } from 'vite';
import 'node:os';
import './chunks/env.Dq0hM4Xv.js';
import 'std-env';

function defineConfig(config) {
	return config;
}
function defineProject(config) {
	return config;
}
function defineWorkspace(config) {
	return config;
}

export { defineConfig, defineProject, defineWorkspace };
