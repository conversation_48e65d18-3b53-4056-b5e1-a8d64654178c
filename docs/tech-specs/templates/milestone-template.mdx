---
title: Milestone <ID> — <One-line scope>
description: <Short paragraph of intent>
created: <YYYY-MM-DD>
version: 0.0.0
status: Draft
tags: [milestone]
authors: []
---

import { Callout } from '@/components/Callout'

<Callout emoji="🚧">
<strong>Draft.</strong> Replace placeholders before PR.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
# pin exact versions or "inherit-from-root"
```

---

## 🎯 Definition of Done

<text>

---

## 📦 Deliverables

| Path | Must contain … |
|------|----------------|
|      |                |

---

## 🗂 Directory / API Diagram

```text
# Tree or sequence diagram as needed
```

---

## 🧠 Key Decisions

| Topic  | Decision | Rationale |
|--------|----------|-----------|
|        |          |           |

---

## ✅ Success Criteria

**AUTHORITATIVE TEST**: `bash scripts/<milestone>-acceptance.sh`

The milestone is complete when the acceptance script passes (exit code 0). This script is the single source of truth for all validation requirements.

**The script validates:**
- [ ] <Validation criterion 1>
- [ ] <Validation criterion 2>
- [ ] <Validation criterion 3>

---

## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules prevent common implementation issues and ensure quality standards.

### Agent Configuration

For complete agent configuration templates, see [Core Agent Rules](../process/agent-rules-core.mdx).

**Replace these placeholders in the templates:**
- `{MILESTONE_ID}` → `<Your milestone ID>` (e.g., "M1")
- `{MILESTONE_TITLE}` → `<Your milestone title>`
- `{milestone_id}` → `<lowercase milestone ID>` (e.g., "milestone-m1")
- `{milestone_script}` → `<acceptance script name>` (e.g., "m1")

### Milestone-Specific Quality Rules
Add any milestone-specific requirements here:
- **<Specific requirement 1>** for this milestone
- **<Specific requirement 2>** for this milestone

### Process Validation Checklist

Before starting implementation, verify:
- [ ] Agent rules configured in your AI assistant (see [Core Agent Rules](../process/agent-rules-core.mdx))
- [ ] Pre-implementation checklist template ready
- [ ] Work log directory structure planned: `work-log/<milestone-id>/`
- [ ] Understanding of all success criteria validated

During implementation, maintain:
- [ ] Real-time work log updates
- [ ] Systematic requirement tracking
- [ ] Immediate validation of completed criteria
- [ ] Documentation sync with actual implementation

After implementation, complete:
- [ ] Full acceptance test validation: `bash scripts/<milestone>-acceptance.sh`
- [ ] Comprehensive work log creation
- [ ] Documentation updates reflecting reality
- [ ] Process improvement analysis

---

## 🔨 Task Breakdown

| #  | Branch name | Checklist item |
|----|-------------|---------------|
|    |             |               |

---

## 🤖 CI Pipeline (ready-to-copy)

```yaml
# Workflow snippet
```

---

## 🧪 Acceptance Testing

**Single command validates everything:**

```bash
bash scripts/<milestone>-acceptance.sh
```

This script is the authoritative definition of all acceptance criteria. See the script implementation in the file stubs below.

---

## 🔄 Document History

| Version | Date | Changes | Author | Milestone Status |
|---------|------|---------|--------|------------------|
| 0.1.0 | <YYYY-MM-DD> | Initial specification | <author> | Draft |

### Status Progression
- **Draft** → **Approved** → **In Progress** → **In Review** → **Completed**

### Update Guidelines
- Increment version for significant changes (new requirements, scope changes)
- Update milestone status when implementation phase changes
- Document all major decisions and scope modifications
- Link to related ADRs when architectural decisions are made

---

## 📚 Related Documentation

### Cross-References
- **ADRs**: List any architectural decisions that impact this milestone
- **Dependencies**: Reference other milestones this depends on
- **Domains**: Link to relevant domain specifications

### External Resources
- **Technical References**: Links to external documentation, APIs, libraries
- **Research**: Background research and analysis documents

<Callout emoji="📝">
Must pass <Link href="../spec-checklist.mdx">spec-checklist</Link> & dry-run before moving to <code>status: Approved</code>.
</Callout>

<Callout emoji="🔗">
Remember to update the <Link href="../milestone-log.mdx">milestone-log.mdx</Link> when this milestone's status or progress changes.
</Callout>