---
title: "Core Process Guidelines & Agent Rules"
description: "Comprehensive process requirements, workflows, and agent configurations for all project activities"
created: "2025-05-25"
updated: "2025-05-25"
version: "2.0.0"
status: "Active"
tags: ["process", "agents", "rules", "workflow", "shared"]
authors: ["nitishMehrotra"]
---

# Core Process Guidelines & Agent Rules

> **📋 Purpose:** This document consolidates all process requirements, workflows, and agent configurations for the WorkflowMapperAgent project. It serves as the single source of truth for how we work.

---

## 🎯 Process Overview

### Process Categories
- **📋 Milestone Implementation** - How to execute milestone specifications
- **🏗️ Architectural Decisions** - How to create and manage ADRs
- **📝 Documentation** - How to maintain specs, templates, and guides
- **🔄 Git Workflow** - Branching, commits, and release processes
- **🤖 Agent Configuration** - AI assistant setup and rules
- **✅ Quality Assurance** - Validation, testing, and review processes

---

## 📋 Milestone Implementation Process

## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules prevent common implementation issues and ensure quality standards.

### Agent Configuration Templates

#### For Claude/Anthropic (System Instructions)
```markdown
You are implementing Milestone {MILESTONE_ID}: {MILESTONE_TITLE}. Follow these mandatory process requirements:

PRE-IMPLEMENTATION RULES:
- MUST create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- MUST validate ALL success criteria are understood and testable
- MUST plan complete dependency compatibility matrix upfront
- MUST design shared configuration strategy to prevent duplication

DURING IMPLEMENTATION RULES:
- MUST update work logs in real-time, not after completion
- MUST update milestone specification during implementation with actual versions
- MUST use systematic requirement tracking with checklists
- MUST validate each success criterion immediately after implementation

POST-IMPLEMENTATION RULES:
- MUST run bash scripts/{milestone_script}-acceptance.sh immediately after implementation
- MUST update all documentation to reflect actual implementation
- MUST create comprehensive work log with lessons learned
- MUST commit with conventional commit messages including detailed descriptions

QUALITY ASSURANCE RULES:
- MUST implement ALL specified success criteria (no skipping)
- MUST use package managers instead of manual file editing
- MUST include comprehensive testing with appropriate coverage
- MUST validate all acceptance criteria before marking complete
```

#### For GitHub Copilot (Workspace Settings)
```json
{
  "github.copilot.chat.codeGeneration.instructions": [
    "Always create work-log/milestone-{milestone_id}/requirement-checklist.md before starting implementation",
    "Update work logs in real-time during implementation, not after completion",
    "Validate ALL success criteria immediately after implementation",
    "Update milestone specifications to reflect actual toolchain versions",
    "Use package managers for dependency management, never edit package files manually",
    "Include comprehensive testing with appropriate coverage thresholds",
    "Run complete acceptance validation immediately after implementation"
  ]
}
```

#### For Cursor (.cursor/rules)
```markdown
# Milestone {MILESTONE_ID} Process Requirements

## Pre-Implementation
- Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- Validate ALL success criteria are understood and testable
- Plan complete dependency compatibility matrix upfront

## During Implementation
- Update work logs in real-time, not after completion
- Update milestone specification during implementation
- Use systematic requirement tracking with checklists

## Post-Implementation
- Run bash scripts/{milestone_script}-acceptance.sh immediately after implementation
- Update all documentation to reflect actual implementation
- Create comprehensive work log with lessons learned

## Quality Assurance
- Implement ALL specified success criteria (no skipping)
- Use package managers instead of manual file editing
- Include comprehensive testing with appropriate coverage
- Validate all acceptance criteria before marking complete
```

#### For Custom Agents (YAML Configuration)
```yaml
agent_rules:
  milestone: "{MILESTONE_ID}"
  title: "{MILESTONE_TITLE}"

  pre_implementation:
    - "Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting"
    - "Validate ALL success criteria are understood and testable"
    - "Plan complete dependency compatibility matrix upfront"
    - "Design shared configuration strategy to prevent duplication"

  during_implementation:
    - "Update work logs in real-time, not after completion"
    - "Update milestone specification during implementation with actual versions"
    - "Use systematic requirement tracking with checklists"
    - "Validate each success criterion immediately after implementation"

  post_implementation:
    - "Run bash scripts/{milestone_script}-acceptance.sh immediately after implementation"
    - "Update all documentation to reflect actual implementation"
    - "Create comprehensive work log with lessons learned"
    - "Commit with conventional commit messages including detailed descriptions"

  quality_assurance:
    - "Implement ALL specified success criteria (no skipping)"
    - "Use package managers instead of manual file editing"
    - "Include comprehensive testing with appropriate coverage"
    - "Validate all acceptance criteria before marking complete"
```

### Process Validation Checklist

Before starting implementation, verify:
- [ ] Agent rules configured in your AI assistant
- [ ] Pre-implementation checklist template ready
- [ ] Work log directory structure planned
- [ ] Understanding of all success criteria validated

During implementation, maintain:
- [ ] Real-time work log updates
- [ ] Systematic requirement tracking
- [ ] Immediate validation of completed criteria
- [ ] Documentation sync with actual implementation

After implementation, complete:
- [ ] Full acceptance test validation
- [ ] Comprehensive work log creation
- [ ] Documentation updates reflecting reality
- [ ] Process improvement analysis

## 🔧 Usage Instructions

### For Milestone Specifications
Reference this file in your milestone specification:

```markdown
## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules prevent the common issues identified in milestone implementation analysis.

### Quick Configuration for {MILESTONE_ID}

For complete agent configuration templates, see [Core Agent Rules](../process/agent-rules-core.mdx).

**Replace these placeholders in the templates:**
- `{MILESTONE_ID}` → `{Your milestone ID}`
- `{MILESTONE_TITLE}` → `{Your milestone title}`
- `{milestone_id}` → `{lowercase milestone ID}`
- `{milestone_script}` → `{acceptance script name}`
```

### For Template Usage
In milestone templates, reference this file:

```markdown
## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation.

See [Core Agent Rules](../process/agent-rules-core.mdx) for complete configuration templates.

Replace placeholders:
- `{MILESTONE_ID}` → Your milestone ID (e.g., "M1")
- `{MILESTONE_TITLE}` → Your milestone title
- `{milestone_id}` → Lowercase milestone ID (e.g., "m1")
- `{milestone_script}` → Acceptance script name (e.g., "m1")
```

## 🔄 Maintenance

### Single Source of Truth
This file is the authoritative source for all agent configuration templates. Updates here automatically apply to all milestones that reference it.

### Version Control
- Update version number when making significant changes
- Document changes in the file header
- Notify milestone owners when breaking changes are made

### Evolution
Process requirements should evolve based on:
- Implementation experience and lessons learned
- Common issues identified across milestones
- Agent behavior patterns and effectiveness
- Quality metrics and success rates

## 🎯 Process Improvements Learned

*Based on M0 implementation experience, these improvements are incorporated into the core rules:*

### Pre-Implementation Validation
- **Create comprehensive requirement checklist** before starting implementation
- **Validate ALL success criteria** are understood and testable
- **Plan dependency compatibility matrix** to avoid version conflicts
- **Design shared configuration strategy** to prevent duplication

### Implementation Efficiency
- **Use workspace inheritance** for common configurations (ESLint, TypeScript, coverage)
- **Batch dependency operations** instead of individual package updates
- **Create shared configuration files** in `packages/config/` for reuse
- **Implement comprehensive validation script** for single-pass testing

### Documentation Process
- **Maintain real-time documentation sync** during implementation, not after
- **Use template-driven work logs** that get filled progressively
- **Update milestone specifications** incrementally during implementation
- **Create technical reference** as configurations are added

### Quality Assurance
- **Validate all success criteria** immediately after implementation
- **Use systematic requirement tracking** with checkboxes
- **Implement comprehensive acceptance testing** including Docker validation
- **Create agent dry-run validation** for setup verification

---

## 🏗️ Architectural Decision Process

### Creating a New ADR
1. **Copy template**: Use [`templates/adr-template.mdx`](../templates/adr-template.mdx)
2. **Create file**: Save as `adr-XXX-short-title.mdx` in `docs/tech-specs/adrs/` directory
3. **Fill details**: Complete all sections with specific information
4. **Set status**: Start with "Proposed"
5. **Add to index**: Update the table in [`adrs/log.mdx`](../adrs/log.mdx)

### Review & Approval Process
1. **Discussion**: Team review and feedback on the ADR
2. **Decision**: Update status to "Accepted", "Rejected", or "Superseded"
3. **Implementation**: Reference ADR in related code/docs
4. **Maintenance**: Periodic review of decisions for continued relevance

### File Naming Convention
- Format: `adr-XXX-short-title.mdx`
- Examples: `adr-001-monorepo.mdx`, `adr-015-database-choice.mdx`
- Use sequential numbering (ADR-001, ADR-002, etc.)

### Status Management
- 🟡 **Proposed**: Under discussion
- ✅ **Accepted**: Approved and implemented
- ❌ **Rejected**: Decided against
- 🔄 **Superseded**: Replaced by newer decision
- ⚠️ **Deprecated**: No longer recommended

---

## 📝 Documentation Process

### Specification Validation
All milestone specifications must pass validation using:
```bash
node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-XX.mdx
```

### Required Sections (from spec_checklist.mdx)
**Front-matter fields:**
- `title`, `description`, `created`, `version`, `status`, `tags`

**Top-level headings (exact text):**
1. `## 🧳 Toolchain Versions`
2. `## 🎯 Definition of Done`
3. `## 📦 Deliverables`
4. `## 🗂 Directory` or `## 🗂 Directory / API Diagram`
5. `## 🧠 Key Decisions`
6. `## ✅ Success Criteria`
7. `## 🔨 Task Breakdown`
8. `## 🤖 CI Pipeline`
9. `## 🧪 Acceptance Tests`

### Template Usage
- Use templates from `docs/tech-specs/templates/` for consistency
- Replace all placeholders before submitting
- Follow established patterns and conventions

---

## 🔄 Git Workflow Process

### Branching Strategy
```
main
├── milestone/m1-static-graph-builder
│   ├── m1/task-01-parser-setup
│   ├── m1/task-02-json-ld-output
│   └── m1/task-03-cli-interface
└── milestone/m2-incremental-diff
    ├── m2/task-01-file-watcher
    └── m2/task-02-diff-algorithm
```

### Branch Naming Conventions
- **Milestone branches**: `milestone/m{X}-{short-description}`
- **Task branches**: `m{X}/task-{##}-{short-description}`
- **Hotfix branches**: `hotfix/{issue-description}`
- **Feature branches**: `feature/{feature-name}`

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`
**Scopes**: `api`, `web`, `shared`, `docs`, `ci`, `deps`

### Release Process
1. **Milestone completion**: All tasks merged to milestone branch
2. **Integration testing**: Full acceptance test suite
3. **Documentation update**: Sync all specs with implementation
4. **Merge to main**: Create merge commit with milestone summary
5. **Tag release**: `git tag v{major}.{minor}.{patch}`
6. **Deploy**: Automated deployment via CI/CD

---

## ✅ Quality Assurance Process

### Pre-Implementation Checklist
- [ ] Agent rules configured in AI assistant
- [ ] Pre-implementation checklist template ready
- [ ] Work log directory structure planned
- [ ] Understanding of all success criteria validated
- [ ] Dependency compatibility matrix planned

### During Implementation Checklist
- [ ] Real-time work log updates
- [ ] Systematic requirement tracking
- [ ] Immediate validation of completed criteria
- [ ] Documentation sync with actual implementation
- [ ] Use package managers instead of manual file editing

### Post-Implementation Checklist
- [ ] Full acceptance test validation
- [ ] Comprehensive work log creation
- [ ] Documentation updates reflecting reality
- [ ] Process improvement analysis
- [ ] Conventional commit messages with detailed descriptions

### Validation Scripts
| Script | Purpose | Usage |
|--------|---------|-------|
| `spec-lint.mjs` | Validate milestone specs | `node scripts/spec-lint.mjs milestones/milestone-M1.mdx` |
| `generate-milestone.mjs` | Create new milestone | `node scripts/generate-milestone.mjs M1 "Static Graph Builder"` |
| `generate-adr.mjs` | Create new ADR | `node scripts/generate-adr.mjs 007 "Database Choice"` |
| `validate-structure.mjs` | Check docs structure | `node scripts/validate-structure.mjs` |

---

## 🔄 Process Consolidation Status

### ✅ Consolidated Content
This document now includes process content previously scattered across:
- **ADR Process**: Moved from `docs/tech-specs/adrs/log.mdx`
- **Documentation Process**: Moved from `docs/tech-specs/spec_checklist.mdx`
- **Git Workflow**: Moved from `docs/tech-specs/structure.mdx`
- **Agent Rules**: Enhanced from original `agent-rules-core.mdx`
- **Quality Assurance**: Consolidated from multiple milestone files

### 📋 Migration Plan
**Phase 1: Consolidation** ✅
- [x] Create comprehensive process document
- [x] Consolidate all process guidelines
- [x] Maintain backward compatibility

**Phase 2: Update References** (Next)
- [ ] Update `docs/tech-specs/adrs/log.mdx` to reference this file
- [ ] Update `docs/tech-specs/milestones/log.mdx` to reference this file
- [ ] Update `docs/tech-specs/structure.mdx` to reference this file
- [ ] Update all milestone specifications to reference this file
- [ ] Update all templates to reference this file

**Phase 3: Cleanup** (Future)
- [ ] Remove duplicated process content from other files
- [ ] Keep only domain-specific content in original locations
- [ ] Ensure single source of truth for all processes

### 🔗 Cross-References
**Files that should reference this document:**
- [`adrs/log.mdx`](../adrs/log.mdx) - For ADR process
- [`milestones/log.mdx`](../milestones/log.mdx) - For milestone process
- [`structure.mdx`](../structure.mdx) - For git workflow
- [`spec_checklist.mdx`](../spec_checklist.mdx) - For validation process
- All milestone specifications - For implementation process
- All templates - For process guidelines

---

## 📚 Quick Reference

### For Milestone Implementation
1. Configure agent rules (see Agent Configuration section above)
2. Follow milestone implementation process
3. Use quality assurance checklists
4. Run validation scripts

### For ADR Creation
1. Use ADR template
2. Follow ADR process
3. Update ADR log
4. Reference in related docs

### For Documentation
1. Use appropriate template
2. Follow validation requirements
3. Run spec-lint before submission
4. Maintain document history

### For Git Workflow
1. Follow branching conventions
2. Use conventional commits
3. Complete quality checklists
4. Run acceptance tests
