---
title: "Core Agent Rules for Milestone Implementation"
description: "Shared process requirements and agent configurations for all milestones"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["process", "agents", "rules", "shared"]
authors: ["nitishMehrotra"]
---

# Core Agent Rules for Milestone Implementation

## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules prevent common implementation issues and ensure quality standards.

### Agent Configuration Templates

#### For Claude/Anthropic (System Instructions)
```markdown
You are implementing Milestone {MILESTONE_ID}: {MILESTONE_TITLE}. Follow these mandatory process requirements:

PRE-IMPLEMENTATION RULES:
- MUST create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- MUST validate ALL success criteria are understood and testable
- MUST plan complete dependency compatibility matrix upfront
- MUST design shared configuration strategy to prevent duplication

DURING IMPLEMENTATION RULES:
- MUST update work logs in real-time, not after completion
- MUST update milestone specification during implementation with actual versions
- MUST use systematic requirement tracking with checklists
- MUST validate each success criterion immediately after implementation

POST-IMPLEMENTATION RULES:
- MUST run bash scripts/{milestone_script}-acceptance.sh immediately after implementation
- MUST update all documentation to reflect actual implementation
- MUST create comprehensive work log with lessons learned
- MUST commit with conventional commit messages including detailed descriptions

QUALITY ASSURANCE RULES:
- MUST implement ALL specified success criteria (no skipping)
- MUST use package managers instead of manual file editing
- MUST include comprehensive testing with appropriate coverage
- MUST validate all acceptance criteria before marking complete
```

#### For GitHub Copilot (Workspace Settings)
```json
{
  "github.copilot.chat.codeGeneration.instructions": [
    "Always create work-log/milestone-{milestone_id}/requirement-checklist.md before starting implementation",
    "Update work logs in real-time during implementation, not after completion",
    "Validate ALL success criteria immediately after implementation",
    "Update milestone specifications to reflect actual toolchain versions",
    "Use package managers for dependency management, never edit package files manually",
    "Include comprehensive testing with appropriate coverage thresholds",
    "Run complete acceptance validation immediately after implementation"
  ]
}
```

#### For Cursor (.cursor/rules)
```markdown
# Milestone {MILESTONE_ID} Process Requirements

## Pre-Implementation
- Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting
- Validate ALL success criteria are understood and testable
- Plan complete dependency compatibility matrix upfront

## During Implementation
- Update work logs in real-time, not after completion
- Update milestone specification during implementation
- Use systematic requirement tracking with checklists

## Post-Implementation
- Run bash scripts/{milestone_script}-acceptance.sh immediately after implementation
- Update all documentation to reflect actual implementation
- Create comprehensive work log with lessons learned

## Quality Assurance
- Implement ALL specified success criteria (no skipping)
- Use package managers instead of manual file editing
- Include comprehensive testing with appropriate coverage
- Validate all acceptance criteria before marking complete
```

#### For Custom Agents (YAML Configuration)
```yaml
agent_rules:
  milestone: "{MILESTONE_ID}"
  title: "{MILESTONE_TITLE}"
  
  pre_implementation:
    - "Create work-log/milestone-{milestone_id}/requirement-checklist.md before starting"
    - "Validate ALL success criteria are understood and testable"
    - "Plan complete dependency compatibility matrix upfront"
    - "Design shared configuration strategy to prevent duplication"
  
  during_implementation:
    - "Update work logs in real-time, not after completion"
    - "Update milestone specification during implementation with actual versions"
    - "Use systematic requirement tracking with checklists"
    - "Validate each success criterion immediately after implementation"
  
  post_implementation:
    - "Run bash scripts/{milestone_script}-acceptance.sh immediately after implementation"
    - "Update all documentation to reflect actual implementation"
    - "Create comprehensive work log with lessons learned"
    - "Commit with conventional commit messages including detailed descriptions"
  
  quality_assurance:
    - "Implement ALL specified success criteria (no skipping)"
    - "Use package managers instead of manual file editing"
    - "Include comprehensive testing with appropriate coverage"
    - "Validate all acceptance criteria before marking complete"
```

### Process Validation Checklist

Before starting implementation, verify:
- [ ] Agent rules configured in your AI assistant
- [ ] Pre-implementation checklist template ready
- [ ] Work log directory structure planned
- [ ] Understanding of all success criteria validated

During implementation, maintain:
- [ ] Real-time work log updates
- [ ] Systematic requirement tracking
- [ ] Immediate validation of completed criteria
- [ ] Documentation sync with actual implementation

After implementation, complete:
- [ ] Full acceptance test validation
- [ ] Comprehensive work log creation
- [ ] Documentation updates reflecting reality
- [ ] Process improvement analysis

## 🔧 Usage Instructions

### For Milestone Specifications
Reference this file in your milestone specification:

```markdown
## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation.

import { ProcessRequirements } from '@/components/ProcessRequirements'

<ProcessRequirements 
  milestoneId="M0" 
  milestoneTitle="Repository Skeleton"
  milestoneScript="m0"
/>

See [Agent Configuration Guide](../agent-configuration-guide.mdx) for complete setup instructions.
```

### For Template Usage
In milestone templates, reference this file:

```markdown
## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation.

See [Core Agent Rules](../process/agent-rules-core.mdx) for complete configuration templates.

Replace placeholders:
- `{MILESTONE_ID}` → Your milestone ID (e.g., "M1")
- `{MILESTONE_TITLE}` → Your milestone title
- `{milestone_id}` → Lowercase milestone ID (e.g., "m1")
- `{milestone_script}` → Acceptance script name (e.g., "m1")
```

## 🔄 Maintenance

### Single Source of Truth
This file is the authoritative source for all agent configuration templates. Updates here automatically apply to all milestones that reference it.

### Version Control
- Update version number when making significant changes
- Document changes in the file header
- Notify milestone owners when breaking changes are made

### Evolution
Process requirements should evolve based on:
- Implementation experience and lessons learned
- Common issues identified across milestones
- Agent behavior patterns and effectiveness
- Quality metrics and success rates
